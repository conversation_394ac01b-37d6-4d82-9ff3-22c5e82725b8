package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;
import com.github.cret.web.oee.service.TheoreticalOutputSampleService;

@RestController
@RequestMapping("/theoretical-output-sample")
public class TheoreticalOutputSampleController {

	private final TheoreticalOutputSampleService theoreticalOutputSampleService;

	public TheoreticalOutputSampleController(TheoreticalOutputSampleService theoreticalOutputSampleService) {
		this.theoreticalOutputSampleService = theoreticalOutputSampleService;
	}

	@RequestMapping("/list")
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples() {
		return theoreticalOutputSampleService.getTheoreticalOutputSamples();
	}

}
