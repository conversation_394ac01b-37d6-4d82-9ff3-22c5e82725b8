package com.github.cret.web.oee.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.TheoreticalOutputSampleService;
import com.github.cret.web.oee.service.TheoreticalOutputService;

@Service
public class TheoreticalOutputSampleServiceImpl implements TheoreticalOutputSampleService {

	private final TheoreticalOutputService theoreticalOutputService;

	private final DeviceService deviceService;

	public TheoreticalOutputSampleServiceImpl(TheoreticalOutputService theoreticalOutputService,
			DeviceService deviceService) {
		this.theoreticalOutputService = theoreticalOutputService;
		this.deviceService = deviceService;
	}

	@Override
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples() {
		List<Device> smtDevices = deviceService.getDevicesByCategory(DeviceCategory.SMT);

		return smtDevices.stream()
			.flatMap(device -> theoreticalOutputService.getTheoreticalOutputSamples(device).stream())
			.distinct()
			.sorted(Comparator.comparing(TheoreticalOutputSample::getLineCode)
				.thenComparing(TheoreticalOutputSample::getDeviceCode))
			.collect(Collectors.toList());
	}

}
